import type { Game, GameFilters, UseGameSearchParams } from '@/types/game.types';

/**
 * Convert UI filters to API search parameters
 */
export function filtersToSearchParams(filters: GameFilters): UseGameSearchParams {
  return {
    name: filters.searchTerm || undefined,
    categories: filters.selectedCategories.length > 0 ? filters.selectedCategories : undefined,
    // Note: Provider filtering might need to be handled differently based on API
    // For now, we'll use the title field for provider filtering
    ...(filters.selectedProvider && filters.selectedProvider !== 'ALL' 
      ? { title: filters.selectedProvider } 
      : {}),
    device: filters.device,
  };
}

/**
 * Filter games locally (for client-side filtering when needed)
 */
export function filterGamesLocally(games: Game[], filters: GameFilters): Game[] {
  return games.filter(game => {
    // Search term filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      if (!game.name.toLowerCase().includes(searchLower) && 
          !game.title.toLowerCase().includes(searchLower)) {
        return false;
      }
    }

    // Category filter
    if (filters.selectedCategories.length > 0) {
      const gameCategories = game.categories.split(',').map(cat => cat.trim().toLowerCase());
      const hasMatchingCategory = filters.selectedCategories.some(selectedCat => 
        gameCategories.includes(selectedCat.toLowerCase())
      );
      if (!hasMatchingCategory) {
        return false;
      }
    }

    // Provider filter
    if (filters.selectedProvider && filters.selectedProvider !== 'ALL') {
      if (!game.title.toLowerCase().includes(filters.selectedProvider.toLowerCase())) {
        return false;
      }
    }

    return true;
  });
}

/**
 * Get unique categories from games array
 */
export function extractCategoriesFromGames(games: Game[]): string[] {
  const categoriesSet = new Set<string>();
  
  games.forEach(game => {
    const categories = game.categories.split(',').map(cat => cat.trim());
    categories.forEach(category => {
      if (category) {
        categoriesSet.add(category);
      }
    });
  });

  return Array.from(categoriesSet).sort();
}

/**
 * Get unique providers/titles from games array
 */
export function extractProvidersFromGames(games: Game[]): string[] {
  const providersSet = new Set<string>();
  
  games.forEach(game => {
    if (game.title) {
      providersSet.add(game.title);
    }
  });

  return Array.from(providersSet).sort();
}

/**
 * Format category name for display
 */
export function formatCategoryName(category: string): string {
  return category
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Check if a game matches the current device
 */
export function isGameCompatibleWithDevice(game: Game, device: string): boolean {
  if (!game.device) return true; // If no device specified, assume compatible
  
  const gameDevices = game.device.split(',').map(d => d.trim().toLowerCase());
  return gameDevices.includes(device.toLowerCase()) || gameDevices.includes('all');
}

/**
 * Get current device type
 */
export function getCurrentDevice(): string {
  if (typeof window === 'undefined') return 'desktop';
  
  const width = window.innerWidth;
  if (width < 768) return 'mobile';
  if (width < 1024) return 'tablet';
  return 'desktop';
}

/**
 * Debounce function for search
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
