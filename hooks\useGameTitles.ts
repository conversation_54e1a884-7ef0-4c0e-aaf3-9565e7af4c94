"use client"

import { useQuery } from '@tanstack/react-query';
import { gameService } from '@/services/game.service';
import { QUERY_KEYS, CACHE_TIMES, DEFAULT_PROVIDERS } from '@/constants/game.constants';
import type { TitlesResponse } from '@/types/game.types';

export interface UseGameTitlesOptions {
  enabled?: boolean;
  staleTime?: number;
  gcTime?: number;
  fallbackData?: string[];
}

export function useGameTitles(options: UseGameTitlesOptions = {}) {
  const {
    enabled = true,
    staleTime = CACHE_TIMES.TITLES,
    gcTime = CACHE_TIMES.TITLES,
    fallbackData = DEFAULT_PROVIDERS,
  } = options;

  return useQuery<TitlesResponse, Error>({
    queryKey: QUERY_KEYS.TITLES,
    queryFn: gameService.getTitles,
    enabled,
    staleTime,
    gcTime,
    placeholderData: fallbackData,
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors
      if (error?.response?.status >= 400 && error?.response?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
    // Refetch in background to keep data fresh
    refetchInterval: 24 * 60 * 60 * 1000, // 24 hours
    refetchIntervalInBackground: true,
  });
}
