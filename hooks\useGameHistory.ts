"use client"

import { useQuery } from '@tanstack/react-query';
import { gameService } from '@/services/game.service';
import { QUERY_KEYS } from '@/constants/game.constants';
import type { GameHistoryParams } from '@/types/game.types';

export interface UseGameHistoryOptions {
  enabled?: boolean;
  staleTime?: number;
  gcTime?: number;
}

export function useGameHistory(
  params: GameHistoryParams = {},
  options: UseGameHistoryOptions = {}
) {
  const {
    enabled = true,
    staleTime = 0, // Game history should always be fresh
    gcTime = 5 * 60 * 1000, // 5 minutes
  } = options;

  return useQuery({
    queryKey: QUERY_KEYS.GAME_HISTORY(params),
    queryFn: () => gameService.getGameHistory(params),
    enabled,
    staleTime,
    gcTime,
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors
      if (error?.response?.status >= 400 && error?.response?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
  });
}
