"use client"

import { useInfiniteQuery } from '@tanstack/react-query';
import { gameService } from '@/services/game.service';
import { QUERY_KEYS, CACHE_TIMES, DEFAULT_PAGE_SIZE } from '@/constants/game.constants';
import type { InfiniteGameSearchParams, GameSearchResponse } from '@/types/game.types';

export interface UseInfiniteGameSearchOptions {
  enabled?: boolean;
  staleTime?: number;
  gcTime?: number;
}

export function useInfiniteGameSearch(
  params: InfiniteGameSearchParams = {},
  options: UseInfiniteGameSearchOptions = {}
) {
  const {
    enabled = true,
    staleTime = CACHE_TIMES.GAME_SEARCH,
    gcTime = CACHE_TIMES.GAME_SEARCH * 2,
  } = options;

  return useInfiniteQuery<GameSearchResponse, Error>({
    queryKey: ['games', 'infinite', params],
    queryFn: async ({ pageParam = 1 }) => {
      return gameService.searchGames({
        ...params,
        page: pageParam as number,
        limit: params.limit || DEFAULT_PAGE_SIZE,
      });
    },
    getNextPageParam: (lastPage) => {
      const { page, totalPages } = lastPage.pagination;
      return page < totalPages ? page + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      const { page } = firstPage.pagination;
      return page > 1 ? page - 1 : undefined;
    },
    enabled,
    staleTime,
    gcTime,
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors
      if (error?.response?.status >= 400 && error?.response?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
    initialPageParam: 1,
  });
}
