"use client"

import { useQuery } from '@tanstack/react-query';
import { gameService } from '@/services/game.service';
import { QUERY_KEYS, CACHE_TIMES } from '@/constants/game.constants';
import type { UseGameSearchParams, GameSearchResponse } from '@/types/game.types';

export interface UseGameSearchOptions {
  enabled?: boolean;
  staleTime?: number;
  gcTime?: number;
}

export function useGameSearch(
  params: UseGameSearchParams = {},
  options: UseGameSearchOptions = {}
) {
  const {
    enabled = true,
    staleTime = CACHE_TIMES.GAME_SEARCH,
    gcTime = CACHE_TIMES.GAME_SEARCH * 2,
  } = options;

  return useQuery<GameSearchResponse, Error>({
    queryKey: QUERY_KEYS.GAME_SEARCH(params),
    queryFn: () => gameService.searchGames(params),
    enabled,
    staleTime,
    gcTime,
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors
      if (error?.response?.status >= 400 && error?.response?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
  });
}
