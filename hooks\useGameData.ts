"use client"

import { useMemo } from 'react';
import { useGameCategories } from './useGameCategories';
import { useGameTitles } from './useGameTitles';
import { useDebouncedGameSearch } from './useDebouncedGameSearch';
import { getCurrentDevice } from '@/utils/game.utils';
import type { GameFilters, UseGameSearchParams } from '@/types/game.types';

export interface UseGameDataParams {
  filters: GameFilters;
  searchEnabled?: boolean;
}

export interface UseGameDataReturn {
  // Search results
  games: any[];
  gamesLoading: boolean;
  gamesError: Error | null;
  hasNextPage: boolean;
  totalGames: number;
  currentPage: number;
  totalPages: number;
  
  // Categories
  categories: string[];
  categoriesLoading: boolean;
  categoriesError: Error | null;
  
  // Providers/Titles
  providers: string[];
  providersLoading: boolean;
  providersError: Error | null;
  
  // Search state
  isSearching: boolean;
  searchTerm: string;
  
  // Combined loading state
  isLoading: boolean;
  hasError: boolean;
}

/**
 * Comprehensive hook that provides all game-related data
 * This is the main hook that components should use
 */
export function useGameData({ 
  filters, 
  searchEnabled = true 
}: UseGameDataParams): UseGameDataReturn {
  
  // Get current device for filtering
  const currentDevice = getCurrentDevice();
  
  // Prepare search parameters
  const searchParams: UseGameSearchParams = useMemo(() => ({
    name: filters.searchTerm || undefined,
    categories: filters.selectedCategories.length > 0 ? filters.selectedCategories : undefined,
    title: filters.selectedProvider && filters.selectedProvider !== 'ALL' 
      ? filters.selectedProvider 
      : undefined,
    device: currentDevice,
  }), [filters, currentDevice]);

  // Fetch categories
  const {
    data: categories = [],
    isLoading: categoriesLoading,
    error: categoriesError,
  } = useGameCategories();

  // Fetch providers/titles
  const {
    data: providers = [],
    isLoading: providersLoading,
    error: providersError,
  } = useGameTitles();

  // Fetch games with debounced search
  const {
    data: gamesData,
    isLoading: gamesLoading,
    error: gamesError,
    searchTerm,
    isSearching,
  } = useDebouncedGameSearch({
    searchTerm: filters.searchTerm,
    categories: filters.selectedCategories.length > 0 ? filters.selectedCategories : undefined,
    title: filters.selectedProvider && filters.selectedProvider !== 'ALL' 
      ? filters.selectedProvider 
      : undefined,
    enabled: searchEnabled,
  });

  // Process games data
  const games = useMemo(() => {
    if (!gamesData?.games) return [];
    
    let filteredGames = gamesData.games;
    
    // Apply favorites filter if needed
    if (filters.showFavoritesOnly) {
      // TODO: Implement favorites filtering when favorites system is ready
      // For now, return empty array or implement local storage favorites
      filteredGames = [];
    }
    
    return filteredGames;
  }, [gamesData?.games, filters.showFavoritesOnly]);

  // Calculate pagination info
  const pagination = gamesData?.pagination;
  const hasNextPage = pagination ? pagination.page < pagination.totalPages : false;
  const totalGames = pagination?.total || 0;
  const currentPage = pagination?.page || 1;
  const totalPages = pagination?.totalPages || 1;

  // Combined loading and error states
  const isLoading = categoriesLoading || providersLoading || gamesLoading;
  const hasError = !!(categoriesError || providersError || gamesError);

  return {
    // Search results
    games,
    gamesLoading,
    gamesError,
    hasNextPage,
    totalGames,
    currentPage,
    totalPages,
    
    // Categories
    categories,
    categoriesLoading,
    categoriesError,
    
    // Providers
    providers,
    providersLoading,
    providersError,
    
    // Search state
    isSearching,
    searchTerm,
    
    // Combined states
    isLoading,
    hasError,
  };
}
