import { apiClient } from '@/services/api.service';
import { toast } from 'sonner';
import type {
  GameSearchParams,
  GameSearchResponse,
  CategoriesResponse,
  TitlesResponse,
  GameUrlResponse,
  GameHistoryParams,
  UseGameSearchParams
} from '@/types/game.types';

const GAMES_BASE_URL = '/v1/games';

export const gameService = {
  /**
   * Search games with filters and pagination
   */
  async searchGames(params: UseGameSearchParams): Promise<GameSearchResponse> {
    try {
      const queryParams = new URLSearchParams();

      // Convert UseGameSearchParams to GameSearchParams for API
      if (params.name) queryParams.set('name', params.name);
      if (params.categories?.length) {
        queryParams.set('categories', params.categories.join(','));
      }
      if (params.device) queryParams.set('device', params.device);
      if (params.page) queryParams.set('page', params.page.toString());
      if (params.limit) queryParams.set('limit', params.limit.toString());
      if (params.sortBy) queryParams.set('sortBy', params.sortBy);
      if (params.sortOrder) queryParams.set('sortOrder', params.sortOrder);

      const url = `${GAMES_BASE_URL}/search${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await apiClient.get(url);

      return response.data;
    } catch (error: any) {
      console.error('Failed to search games:', error);

      // Show toast for non-401 errors (401 is handled by interceptor)
      if (error?.response?.status !== 401 && typeof window !== 'undefined') {
        toast.error('Failed to load games. Please try again.');
      }

      throw error;
    }
  },

  /**
   * Get all available game categories
   */
  async getCategories(): Promise<CategoriesResponse> {
    try {
      const response = await apiClient.get(`${GAMES_BASE_URL}/categories`);
      return response.data;
    } catch (error: any) {
      console.error('Failed to get categories:', error);

      if (error?.response?.status !== 401 && typeof window !== 'undefined') {
        toast.error('Failed to load categories. Please try again.');
      }

      throw error;
    }
  },

  /**
   * Get all game titles/providers
   */
  async getTitles(): Promise<TitlesResponse> {
    try {
      const response = await apiClient.get(`${GAMES_BASE_URL}/titles`);
      return response.data;
    } catch (error: any) {
      console.error('Failed to get titles:', error);

      if (error?.response?.status !== 401 && typeof window !== 'undefined') {
        toast.error('Failed to load providers. Please try again.');
      }

      throw error;
    }
  },

  /**
   * Get game URL to launch the game
   */
  async getGameUrl(gameId: string): Promise<GameUrlResponse> {
    try {
      const response = await apiClient.get(`${GAMES_BASE_URL}/${gameId}`);
      return response.data;
    } catch (error: any) {
      console.error('Failed to get game URL:', error);

      if (error?.response?.status !== 401 && typeof window !== 'undefined') {
        toast.error('Failed to launch game. Please try again.');
      }

      throw error;
    }
  },

  /**
   * Get game history with pagination and filters
   */
  async getGameHistory(params: GameHistoryParams = {}): Promise<any> {
    try {
      const queryParams = new URLSearchParams();

      if (params.page) queryParams.set('page', params.page.toString());
      if (params.limit) queryParams.set('limit', params.limit.toString());
      if (params.startDate) queryParams.set('startDate', params.startDate);
      if (params.endDate) queryParams.set('endDate', params.endDate);
      if (params.type) queryParams.set('type', params.type);

      const url = `${GAMES_BASE_URL}/history/all${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await apiClient.get(url);

      return response.data;
    } catch (error: any) {
      console.error('Failed to get game history:', error);

      if (error?.response?.status !== 401 && typeof window !== 'undefined') {
        toast.error('Failed to load game history. Please try again.');
      }

      throw error;
    }
  }
};
