"use client"

import { useState, useCallback } from 'react';
import type { GameFilters } from '@/types/game.types';

export interface UseGameFiltersReturn {
  filters: GameFilters;
  updateSearchTerm: (term: string) => void;
  updateCategories: (categories: string[]) => void;
  updateProvider: (provider: string) => void;
  toggleFavorites: () => void;
  resetFilters: () => void;
  setFilters: (filters: Partial<GameFilters>) => void;
}

const initialFilters: GameFilters = {
  searchTerm: '',
  selectedCategories: [],
  selectedProvider: 'ALL',
  showFavoritesOnly: false,
};

export function useGameFilters(defaultFilters?: Partial<GameFilters>): UseGameFiltersReturn {
  const [filters, setFiltersState] = useState<GameFilters>({
    ...initialFilters,
    ...defaultFilters,
  });

  const updateSearchTerm = useCallback((term: string) => {
    setFiltersState(prev => ({
      ...prev,
      searchTerm: term,
    }));
  }, []);

  const updateCategories = useCallback((categories: string[]) => {
    setFiltersState(prev => ({
      ...prev,
      selectedCategories: categories,
    }));
  }, []);

  const updateProvider = useCallback((provider: string) => {
    setFiltersState(prev => ({
      ...prev,
      selectedProvider: provider,
    }));
  }, []);

  const toggleFavorites = useCallback(() => {
    setFiltersState(prev => ({
      ...prev,
      showFavoritesOnly: !prev.showFavoritesOnly,
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setFiltersState({
      ...initialFilters,
      ...defaultFilters,
    });
  }, [defaultFilters]);

  const setFilters = useCallback((newFilters: Partial<GameFilters>) => {
    setFiltersState(prev => ({
      ...prev,
      ...newFilters,
    }));
  }, []);

  return {
    filters,
    updateSearchTerm,
    updateCategories,
    updateProvider,
    toggleFavorites,
    resetFilters,
    setFilters,
  };
}
