import type { SortOption } from '@/types/game.types';

// Default pagination settings
export const DEFAULT_PAGE_SIZE = 20;
export const MAX_PAGE_SIZE = 100;
export const DEFAULT_PAGE = 1;

// Search debounce delay in milliseconds
export const SEARCH_DEBOUNCE_DELAY = 300;

// Cache times (in milliseconds)
export const CACHE_TIMES = {
  GAME_SEARCH: 2 * 60 * 1000,      // 2 minutes
  CATEGORIES: 24 * 60 * 60 * 1000,  // 24 hours
  TITLES: 24 * 60 * 60 * 1000,      // 24 hours
  GAME_URL: 5 * 60 * 1000,          // 5 minutes
} as const;

// Sort options for the UI
export const SORT_OPTIONS: SortOption[] = [
  { value: 'name', label: 'Name' },
  { value: 'title', label: 'Provider' },
];

// Default sort settings
export const DEFAULT_SORT_BY = 'name';
export const DEFAULT_SORT_ORDER = 'ASC';

// Device types
export const DEVICE_TYPES = {
  DESKTOP: 'desktop',
  MOBILE: 'mobile',
  TABLET: 'tablet',
  ALL: 'all',
} as const;

// Default categories (fallback if API fails)
export const DEFAULT_CATEGORIES = [
  'All',
  'Popular', 
  'ShowGames',
  'Favorites',
  'New',
  'Slots',
  'Live',
  'Table',
  'Jackpot',
  'Video Poker',
  'Scratch'
];

// Default providers (fallback if API fails)
export const DEFAULT_PROVIDERS = [
  'ALL',
  'EVOLUTION',
  'PRAGMATICPLAYLIVE',
  'EZUGI',
  'TVBET',
  'ALG',
  'VIVOGAMING',
  'NETENT',
  'PLAYTECH',
  'MICROGAMING',
  'QUICKSPIN',
  'YGGDRASIL',
  'PLAYNGO',
  'REDTIGER',
  'BETSOFT',
  'HABANERO',
];

// Query keys for React Query
export const QUERY_KEYS = {
  GAMES: ['games'] as const,
  GAME_SEARCH: (params: any) => ['games', 'search', params] as const,
  CATEGORIES: ['games', 'categories'] as const,
  TITLES: ['games', 'titles'] as const,
  GAME_URL: (gameId: string) => ['games', 'url', gameId] as const,
  GAME_HISTORY: (params: any) => ['games', 'history', params] as const,
} as const;

// Error messages
export const ERROR_MESSAGES = {
  SEARCH_FAILED: 'Failed to search games. Please try again.',
  CATEGORIES_FAILED: 'Failed to load categories. Please try again.',
  TITLES_FAILED: 'Failed to load providers. Please try again.',
  GAME_LAUNCH_FAILED: 'Failed to launch game. Please try again.',
  HISTORY_FAILED: 'Failed to load game history. Please try again.',
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',
} as const;

// Loading states
export const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
} as const;

// Responsive breakpoints (matching Tailwind defaults)
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
} as const;
