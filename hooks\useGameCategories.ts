"use client"

import { useQuery } from '@tanstack/react-query';
import { gameService } from '@/services/game.service';
import { QUERY_KEYS, CACHE_TIMES, DEFAULT_CATEGORIES } from '@/constants/game.constants';
import type { CategoriesResponse } from '@/types/game.types';

export interface UseGameCategoriesOptions {
  enabled?: boolean;
  staleTime?: number;
  gcTime?: number;
  fallbackData?: string[];
}

export function useGameCategories(options: UseGameCategoriesOptions = {}) {
  const {
    enabled = true,
    staleTime = CACHE_TIMES.CATEGORIES,
    gcTime = CACHE_TIMES.CATEGORIES,
    fallbackData = DEFAULT_CATEGORIES,
  } = options;

  return useQuery<CategoriesResponse, Error>({
    queryKey: QUERY_KEYS.CATEGORIES,
    queryFn: gameService.getCategories,
    enabled,
    staleTime,
    gcTime,
    placeholderData: fallbackData,
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors
      if (error?.response?.status >= 400 && error?.response?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
    // Refetch in background to keep data fresh
    refetchInterval: 24 * 60 * 60 * 1000, // 24 hours
    refetchIntervalInBackground: true,
  });
}
