"use client"

import { useMemo } from 'react';
import { useDebounce } from 'use-debounce';
import { useGameSearch } from './useGameSearch';
import { SEARCH_DEBOUNCE_DELAY, DEFAULT_PAGE_SIZE } from '@/constants/game.constants';
import type { UseGameSearchParams } from '@/types/game.types';

export interface UseDebouncedGameSearchParams extends Omit<UseGameSearchParams, 'name'> {
  searchTerm: string;
  delay?: number;
  enabled?: boolean;
}

export function useDebouncedGameSearch({
  searchTerm,
  delay = SEARCH_DEBOUNCE_DELAY,
  enabled = true,
  ...otherParams
}: UseDebouncedGameSearchParams) {
  // Debounce the search term
  const [debouncedSearchTerm] = useDebounce(searchTerm, delay);

  // Create search parameters with debounced search term
  const searchParams = useMemo(() => ({
    name: debouncedSearchTerm || undefined,
    page: 1, // Reset to first page on new search
    limit: DEFAULT_PAGE_SIZE,
    ...otherParams,
  }), [debouncedSearchTerm, otherParams]);

  // Only enable the query if enabled is true and we have a search term or other filters
  const shouldEnable = enabled && (
    debouncedSearchTerm || 
    otherParams.categories?.length || 
    otherParams.title ||
    otherParams.device
  );

  const result = useGameSearch(searchParams, {
    enabled: shouldEnable,
  });

  return {
    ...result,
    searchTerm: debouncedSearchTerm,
    isSearching: searchTerm !== debouncedSearchTerm,
  };
}
