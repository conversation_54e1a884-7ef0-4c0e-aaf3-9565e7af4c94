# Games API Integration Guide for AI Coding Agents

## Overview
This guide provides comprehensive instructions for AI coding agents to integrate with the casino games API. The backend uses Redis caching for optimal performance, and the frontend should use **React Query (TanStack Query)** for efficient data fetching and caching.

## 🎯 **Recommended Frontend Stack**
- **React Query (TanStack Query)** - HIGHLY RECOMMENDED
  - Perfect synergy with Redis backend caching
  - Automatic background refetching
  - Optimistic updates
  - Built-in loading/error states
  - Cache invalidation strategies

## 🔗 **API Endpoints Overview**

### Base URL: `http://localhost:3000/api/v1/games`

| Endpoint | Method | Purpose | Cache Strategy |
|----------|--------|---------|----------------|
| `/search` | GET | Search games with filters | 1 hour Redis cache |
| `/categories` | GET | Get all game categories | 7 days Redis cache |
| `/titles` | GET | Get all game titles/providers | 7 days Redis cache |
| `/history/all` | GET | Get all game history | No cache |

## 🔍 **1. Game Search API**

### Endpoint: `GET /api/v1/games/search`

**Purpose**: Search and filter games with pagination

**Query Parameters**:
```typescript
interface GameSearchParams {
  name?: string;           // Game name search
  title?: string;          // Game title search
  categories?: string;     // Comma-separated categories
  device?: string;         // Device type filter
  page?: number;           // Page number (default: 1)
  limit?: number;          // Items per page (default: 20, max: 100)
  sortBy?: 'name' | 'title'; // Sort column (default: 'name')
  sortOrder?: 'ASC' | 'DESC'; // Sort order (default: 'ASC')
}
```

**Response Format**:
```typescript
interface GameSearchResponse {
  games: Game[];
  pagination: {
    total: number;
    page: number;
    totalPages: number;
    limit: number;
  };
}

interface Game {
  id: string;
  name: string;
  title: string;
  img: string;
  device: string;
  categories: string;
  flash: boolean;
}
```

**Example Requests**:
```bash
# Basic search
GET /api/v1/games/search?name=slot&page=1&limit=20

# Advanced filtering
GET /api/v1/games/search?categories=slots,table&device=desktop&sortBy=name&sortOrder=ASC

# Search by provider/title
GET /api/v1/games/search?title=pragmatic&page=2&limit=10
```

## 📋 **2. Categories API**

### Endpoint: `GET /api/v1/games/categories`

**Purpose**: Get all available game categories for filter dropdowns

**Response Format**:
```typescript
type CategoriesResponse = string[];
```

**Example Response**:
```json
[
  "slots",
  "table",
  "live",
  "jackpot",
  "video-poker",
  "scratch"
]
```

**Usage**: Perfect for populating category filter dropdowns

## 🏢 **3. Titles/Providers API**

### Endpoint: `GET /api/v1/games/titles`

**Purpose**: Get all game titles/providers for filter dropdowns

**Response Format**:
```typescript
type TitlesResponse = string[];
```

**Example Response**:
```json
[
  "Pragmatic Play",
  "NetEnt",
  "Microgaming",
  "Evolution Gaming",
  "Play'n GO"
]
```

**Usage**: Perfect for populating provider filter dropdowns

## 🎮 **4. Play Game API**

### Endpoint: `GET /api/v1/games/:gameId`

**Purpose**: Get game URL to launch the game

**Authentication**: Required (Player role or higher)

**Response Format**:
```typescript
interface GameUrlResponse {
  url: string;
}
```

**Example Request**:
```bash
GET /api/v1/games/12345
```

## 📊 **5. Game History APIs**

### Get All Game History
**Endpoint**: `GET /api/v1/games/history/all`


**Query Parameters** :
```typescript
interface HistoryParams {
  page?: number;
  limit?: number;
  startDate?: string; // ISO date
  endDate?: string;   // ISO date
  type?: 'add' | 'deduct'; // Transaction type filter
}
```

## ⚛️ **React Query Implementation Examples**

### 1. Setup React Query Client

```typescript
// queryClient.ts
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 2,
      refetchOnWindowFocus: false,
    },
  },
});
```

### 2. Game Search Hook

```typescript
// hooks/useGameSearch.ts
import { useQuery } from '@tanstack/react-query';

interface UseGameSearchParams {
  name?: string;
  categories?: string[];
  device?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'title';
  sortOrder?: 'ASC' | 'DESC';
}

export const useGameSearch = (params: UseGameSearchParams) => {
  const queryParams = new URLSearchParams();

  if (params.name) queryParams.set('name', params.name);
  if (params.categories?.length) queryParams.set('categories', params.categories.join(','));
  if (params.device) queryParams.set('device', params.device);
  if (params.page) queryParams.set('page', params.page.toString());
  if (params.limit) queryParams.set('limit', params.limit.toString());
  if (params.sortBy) queryParams.set('sortBy', params.sortBy);
  if (params.sortOrder) queryParams.set('sortOrder', params.sortOrder);

  return useQuery({
    queryKey: ['games', 'search', params],
    queryFn: async () => {
      const response = await fetch(`/api/v1/games/search?${queryParams}`);
      if (!response.ok) throw new Error('Failed to fetch games');
      return response.json();
    },
    staleTime: 2 * 60 * 1000, // 2 minutes (shorter for search results)
    enabled: true, // Always enabled, can be conditional
  });
};
```

### 3. Categories Hook

```typescript
// hooks/useCategories.ts
import { useQuery } from '@tanstack/react-query';

export const useCategories = () => {
  return useQuery({
    queryKey: ['games', 'categories'],
    queryFn: async () => {
      const response = await fetch('/api/v1/games/categories');
      if (!response.ok) throw new Error('Failed to fetch categories');
      return response.json() as string[];
    },
    staleTime: 24 * 60 * 60 * 1000, // 24 hours (categories rarely change)
    cacheTime: 24 * 60 * 60 * 1000,
  });
};
```

### 4. Titles/Providers Hook

```typescript
// hooks/useTitles.ts
import { useQuery } from '@tanstack/react-query';

export const useTitles = () => {
  return useQuery({
    queryKey: ['games', 'titles'],
    queryFn: async () => {
      const response = await fetch('/api/v1/games/titles');
      if (!response.ok) throw new Error('Failed to fetch titles');
      return response.json() as string[];
    },
    staleTime: 24 * 60 * 60 * 1000, // 24 hours
    cacheTime: 24 * 60 * 60 * 1000,
  });
};
```

### 5. Game Component Example

```typescript
// components/GameSearch.tsx
import React, { useState } from 'react';
import { useGameSearch, useCategories, useTitles } from '../hooks';

export const GameSearch: React.FC = () => {
  const [filters, setFilters] = useState({
    name: '',
    categories: [] as string[],
    page: 1,
    limit: 20,
  });

  const { data: games, isLoading: gamesLoading, error: gamesError } = useGameSearch(filters);
  const { data: categories, isLoading: categoriesLoading } = useCategories();
  const { data: titles, isLoading: titlesLoading } = useTitles();

  if (gamesLoading) return <div>Loading games...</div>;
  if (gamesError) return <div>Error loading games</div>;

  return (
    <div>
      {/* Filter UI */}
      <div className="filters">
        <input
          type="text"
          placeholder="Search games..."
          value={filters.name}
          onChange={(e) => setFilters(prev => ({ ...prev, name: e.target.value, page: 1 }))}
        />

        <select
          multiple
          value={filters.categories}
          onChange={(e) => {
            const selected = Array.from(e.target.selectedOptions, option => option.value);
            setFilters(prev => ({ ...prev, categories: selected, page: 1 }));
          }}
        >
          {categories?.map(category => (
            <option key={category} value={category}>{category}</option>
          ))}
        </select>
      </div>

      {/* Games Grid */}
      <div className="games-grid">
        {games?.games.map(game => (
          <div key={game.id} className="game-card">
            <img src={game.img} alt={game.name} />
            <h3>{game.name}</h3>
            <p>{game.title}</p>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="pagination">
        <button
          disabled={filters.page === 1}
          onClick={() => setFilters(prev => ({ ...prev, page: prev.page - 1 }))}
        >
          Previous
        </button>
        <span>Page {filters.page} of {games?.pagination.totalPages}</span>
        <button
          disabled={filters.page === games?.pagination.totalPages}
          onClick={() => setFilters(prev => ({ ...prev, page: prev.page + 1 }))}
        >
          Next
        </button>
      </div>
    </div>
  );
};
```

## 🚀 **Performance Optimization Tips**

### 1. Cache Strategy Alignment
- **Backend Redis**: 1 hour for search, 7 days for categories/titles
- **Frontend React Query**: 2 minutes for search, 24 hours for categories/titles
- This creates a perfect caching hierarchy

### 2. Query Key Strategy
```typescript
// Good query key structure
['games', 'search', { name: 'slot', page: 1, categories: ['slots'] }]
['games', 'categories']
['games', 'titles']
```

### 3. Prefetching
```typescript
// Prefetch next page
const prefetchNextPage = () => {
  queryClient.prefetchQuery({
    queryKey: ['games', 'search', { ...filters, page: filters.page + 1 }],
    queryFn: () => fetchGames({ ...filters, page: filters.page + 1 }),
  });
};
```

### 4. Background Refetching
```typescript
// Refetch categories/titles in background daily
useQuery({
  queryKey: ['games', 'categories'],
  queryFn: fetchCategories,
  refetchInterval: 24 * 60 * 60 * 1000, // 24 hours
  refetchIntervalInBackground: true,
});
```

## 🔐 **Authentication Integration**

All game-related endpoints require authentication. Include the authentication token in requests:

```typescript
// With axios interceptor
axios.defaults.withCredentials = true; // For HTTP-only cookies

// Or manual header (if using token-based auth)
const fetchWithAuth = async (url: string) => {
  const response = await fetch(url, {
    credentials: 'include', // Include HTTP-only cookies
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return response;
};
```


## 🔄 **Advanced React Query Patterns**

### 1. Infinite Scrolling for Games

```typescript
// hooks/useInfiniteGameSearch.ts
import { useInfiniteQuery } from '@tanstack/react-query';

export const useInfiniteGameSearch = (filters: Omit<UseGameSearchParams, 'page'>) => {
  return useInfiniteQuery({
    queryKey: ['games', 'infinite', filters],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await fetch(`/api/v1/games/search?${new URLSearchParams({
        ...filters,
        page: pageParam.toString(),
        limit: '20'
      })}`);
      return response.json();
    },
    getNextPageParam: (lastPage) => {
      const { page, totalPages } = lastPage.pagination;
      return page < totalPages ? page + 1 : undefined;
    },
    staleTime: 2 * 60 * 1000,
  });
};

// Component usage
const InfiniteGameList = () => {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteGameSearch({ name: searchTerm });

  return (
    <div>
      {data?.pages.map((page, i) => (
        <div key={i}>
          {page.games.map(game => (
            <GameCard key={game.id} game={game} />
          ))}
        </div>
      ))}

      <button
        onClick={() => fetchNextPage()}
        disabled={!hasNextPage || isFetchingNextPage}
      >
        {isFetchingNextPage ? 'Loading...' : 'Load More'}
      </button>
    </div>
  );
};
```

### 2. Real-time Game Updates with Mutations

```typescript
// hooks/useGameMutations.ts
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const useInitializeGames = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/v1/games/init', {
        method: 'POST',
        credentials: 'include',
      });
      if (!response.ok) throw new Error('Failed to initialize games');
      return response.json();
    },
    onSuccess: () => {
      // Invalidate all game-related queries
      queryClient.invalidateQueries({ queryKey: ['games'] });
    },
  });
};

// Usage in admin component
const AdminGameManagement = () => {
  const initializeGames = useInitializeGames();

  const handleInitialize = () => {
    initializeGames.mutate();
  };

  return (
    <button
      onClick={handleInitialize}
      disabled={initializeGames.isLoading}
    >
      {initializeGames.isLoading ? 'Initializing...' : 'Initialize Games'}
    </button>
  );
};
```

### 3. Optimistic Game Favorites

```typescript
// hooks/useGameFavorites.ts
export const useToggleFavorite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ gameId, isFavorite }: { gameId: string; isFavorite: boolean }) => {
      const response = await fetch(`/api/v1/games/${gameId}/favorite`, {
        method: isFavorite ? 'DELETE' : 'POST',
        credentials: 'include',
      });
      return response.json();
    },
    onMutate: async ({ gameId, isFavorite }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['games', 'favorites'] });

      // Snapshot previous value
      const previousFavorites = queryClient.getQueryData(['games', 'favorites']);

      // Optimistically update
      queryClient.setQueryData(['games', 'favorites'], (old: string[] = []) => {
        return isFavorite
          ? old.filter(id => id !== gameId)
          : [...old, gameId];
      });

      return { previousFavorites };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      queryClient.setQueryData(['games', 'favorites'], context?.previousFavorites);
    },
    onSettled: () => {
      // Refetch after mutation
      queryClient.invalidateQueries({ queryKey: ['games', 'favorites'] });
    },
  });
};
```

## 📱 **Mobile-Optimized Patterns**

### 1. Reduced Data Loading for Mobile

```typescript
// hooks/useResponsiveGameSearch.ts
import { useMediaQuery } from 'react-responsive';

export const useResponsiveGameSearch = (params: UseGameSearchParams) => {
  const isMobile = useMediaQuery({ maxWidth: 768 });

  return useGameSearch({
    ...params,
    limit: isMobile ? 10 : 20, // Smaller pages on mobile
  });
};
```

### 2. Image Lazy Loading Integration

```typescript
// components/GameCard.tsx
import { useInView } from 'react-intersection-observer';

const GameCard = ({ game }: { game: Game }) => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <div ref={ref} className="game-card">
      {inView ? (
        <img src={game.img} alt={game.name} loading="lazy" />
      ) : (
        <div className="image-placeholder" />
      )}
      <h3>{game.name}</h3>
    </div>
  );
};
```

## 🔍 **Search Debouncing Pattern**

```typescript
// hooks/useDebouncedGameSearch.ts
import { useMemo } from 'react';
import { useDebounce } from 'use-debounce';

export const useDebouncedGameSearch = (searchTerm: string, delay = 300) => {
  const [debouncedSearchTerm] = useDebounce(searchTerm, delay);

  const searchParams = useMemo(() => ({
    name: debouncedSearchTerm,
    page: 1,
  }), [debouncedSearchTerm]);

  return useGameSearch(searchParams);
};

// Usage
const SearchInput = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const { data, isLoading } = useDebouncedGameSearch(searchTerm);

  return (
    <div>
      <input
        type="text"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        placeholder="Search games..."
      />
      {isLoading && <span>Searching...</span>}
    </div>
  );
};
```

## 🎯 **Error Handling Best Practices**

```typescript
// utils/errorHandling.ts
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        return failureCount < 3;
      },
      onError: (error: any) => {
        // Global error handling
        if (error?.status === 401) {
          // Redirect to login
          window.location.href = '/login';
        }
      },
    },
  },
});

// Component error boundary
const GameSearchWithErrorBoundary = () => {
  const { data, error, isError } = useGameSearch(filters);

  if (isError) {
    return (
      <div className="error-state">
        <h3>Unable to load games</h3>
        <p>{error?.message || 'Something went wrong'}</p>
        <button onClick={() => queryClient.invalidateQueries(['games'])}>
          Try Again
        </button>
      </div>
    );
  }

  return <GameList games={data?.games} />;
};
```

## 📊 **Performance Monitoring**

```typescript
// utils/performanceMonitoring.ts
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      onSuccess: (data, query) => {
        // Log successful queries for monitoring
        console.log(`Query ${query.queryKey.join('.')} completed in ${Date.now() - query.state.dataUpdatedAt}ms`);
      },
      onError: (error, query) => {
        // Log errors for monitoring
        console.error(`Query ${query.queryKey.join('.')} failed:`, error);
      },
    },
  },
});
```

This comprehensive guide provides everything an AI coding agent needs to build a high-performance, user-friendly gaming interface with optimal caching strategies! 🎮✨
