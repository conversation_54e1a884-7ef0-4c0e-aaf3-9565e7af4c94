"use client"

import { useMutation, useQuery } from '@tanstack/react-query';
import { gameService } from '@/services/game.service';
import { QUERY_KEYS, CACHE_TIMES } from '@/constants/game.constants';
import type { GameUrlResponse } from '@/types/game.types';
import { toast } from 'sonner';

export interface UseGameLaunchOptions {
  onSuccess?: (url: string) => void;
  onError?: (error: Error) => void;
}

/**
 * Hook for getting game URL (query-based)
 */
export function useGameUrl(gameId: string | null, enabled: boolean = true) {
  return useQuery<GameUrlResponse, Error>({
    queryKey: QUERY_KEYS.GAME_URL(gameId || ''),
    queryFn: () => gameService.getGameUrl(gameId!),
    enabled: enabled && !!gameId,
    staleTime: CACHE_TIMES.GAME_URL,
    gcTime: CACHE_TIMES.GAME_URL * 2,
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors
      if (error?.response?.status >= 400 && error?.response?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
  });
}

/**
 * Hook for launching a game (mutation-based)
 */
export function useGameLaunch(options: UseGameLaunchOptions = {}) {
  const { onSuccess, onError } = options;

  return useMutation<GameUrlResponse, Error, string>({
    mutationFn: (gameId: string) => gameService.getGameUrl(gameId),
    onSuccess: (data) => {
      if (data.url) {
        // Open game in new window/tab
        window.open(data.url, '_blank', 'noopener,noreferrer');
        onSuccess?.(data.url);
      } else {
        toast.error('Game URL not available');
      }
    },
    onError: (error) => {
      console.error('Failed to launch game:', error);
      toast.error('Failed to launch game. Please try again.');
      onError?.(error);
    },
  });
}

/**
 * Utility function to launch a game directly
 */
export function launchGame(gameId: string) {
  return gameService.getGameUrl(gameId).then(response => {
    if (response.url) {
      window.open(response.url, '_blank', 'noopener,noreferrer');
      return response.url;
    } else {
      throw new Error('Game URL not available');
    }
  });
}
