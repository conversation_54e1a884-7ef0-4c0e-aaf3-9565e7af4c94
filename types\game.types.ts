// Game API Types based on game-api.md documentation

export interface Game {
  id: string;
  name: string;
  title: string;
  img: string;
  device: string;
  categories: string;
  flash: boolean;
}

export interface GameSearchParams {
  name?: string;           // Game name search
  title?: string;          // Game title search
  categories?: string;     // Comma-separated categories
  device?: string;         // Device type filter
  page?: number;           // Page number (default: 1)
  limit?: number;          // Items per page (default: 20, max: 100)
  sortBy?: 'name' | 'title'; // Sort column (default: 'name')
  sortOrder?: 'ASC' | 'DESC'; // Sort order (default: 'ASC')
}

export interface GameSearchResponse {
  games: Game[];
  pagination: {
    total: number;
    page: number;
    totalPages: number;
    limit: number;
  };
}

export interface GameUrlResponse {
  url: string;
}

// Categories and Titles responses
export type CategoriesResponse = string[];
export type TitlesResponse = string[];

// Game History Types
export interface GameHistoryParams {
  page?: number;
  limit?: number;
  startDate?: string; // ISO date
  endDate?: string;   // ISO date
  type?: 'add' | 'deduct'; // Transaction type filter
}

// Hook parameter types for easier usage
export interface UseGameSearchParams {
  name?: string;
  categories?: string[];  // Array instead of comma-separated string
  device?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'title';
  sortOrder?: 'ASC' | 'DESC';
}

// Filter state types for UI components
export interface GameFilters {
  searchTerm: string;
  selectedCategories: string[];
  selectedProvider: string;
  showFavoritesOnly: boolean;
  device?: string;
}

// UI State types
export interface GameUIState {
  activeCategory: string;
  searchOpen: boolean;
  showProviderDropdown: boolean;
  isLoading: boolean;
  error: string | null;
}

// Device types
export type DeviceType = 'desktop' | 'mobile' | 'tablet';

// Sort options
export type SortOption = {
  value: 'name' | 'title';
  label: string;
};

export type SortOrder = 'ASC' | 'DESC';

// API Error types
export interface ApiError {
  status: number;
  message: string;
  data?: any;
}

// Loading states
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// Infinite query types for scrolling
export interface InfiniteGameSearchParams extends Omit<UseGameSearchParams, 'page'> {
  // Inherits all UseGameSearchParams except page
}

export interface InfiniteGameSearchResponse {
  pages: GameSearchResponse[];
  pageParams: number[];
}
